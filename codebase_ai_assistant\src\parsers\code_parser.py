"""
代码解析器模块
使用Tree-sitter和AST解析不同语言的代码
"""
import ast
import os
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import tree_sitter
from tree_sitter import Language, Parser
import logging

from ..core.config import settings, LANGUAGE_EXTENSIONS

logger = logging.getLogger(__name__)


class CodeElement:
    """代码元素基类"""
    
    def __init__(self, name: str, element_type: str, start_line: int, end_line: int, 
                 content: str, file_path: str):
        self.name = name
        self.element_type = element_type  # function, class, variable, etc.
        self.start_line = start_line
        self.end_line = end_line
        self.content = content
        self.file_path = file_path
        self.docstring: Optional[str] = None
        self.parameters: List[str] = []
        self.return_type: Optional[str] = None
        self.complexity: int = 0


class PythonParser:
    """Python代码解析器"""
    
    def parse_file(self, file_path: str) -> List[CodeElement]:
        """解析Python文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            elements = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    element = self._parse_function(node, content, file_path)
                    elements.append(element)
                elif isinstance(node, ast.ClassDef):
                    element = self._parse_class(node, content, file_path)
                    elements.append(element)
                elif isinstance(node, ast.Assign):
                    element = self._parse_variable(node, content, file_path)
                    if element:
                        elements.append(element)
            
            return elements
            
        except Exception as e:
            logger.error(f"解析Python文件失败 {file_path}: {e}")
            return []
    
    def _parse_function(self, node: ast.FunctionDef, content: str, file_path: str) -> CodeElement:
        """解析函数定义"""
        lines = content.split('\n')
        start_line = node.lineno
        end_line = node.end_lineno or start_line
        
        # 提取函数内容
        func_content = '\n'.join(lines[start_line-1:end_line])
        
        element = CodeElement(
            name=node.name,
            element_type="function",
            start_line=start_line,
            end_line=end_line,
            content=func_content,
            file_path=file_path
        )
        
        # 提取文档字符串
        if (node.body and isinstance(node.body[0], ast.Expr) and 
            isinstance(node.body[0].value, ast.Constant) and 
            isinstance(node.body[0].value.value, str)):
            element.docstring = node.body[0].value.value
        
        # 提取参数
        element.parameters = [arg.arg for arg in node.args.args]
        
        # 提取返回类型
        if node.returns:
            element.return_type = ast.unparse(node.returns)
        
        # 计算复杂度（简单的圈复杂度）
        element.complexity = self._calculate_complexity(node)
        
        return element
    
    def _parse_class(self, node: ast.ClassDef, content: str, file_path: str) -> CodeElement:
        """解析类定义"""
        lines = content.split('\n')
        start_line = node.lineno
        end_line = node.end_lineno or start_line
        
        class_content = '\n'.join(lines[start_line-1:end_line])
        
        element = CodeElement(
            name=node.name,
            element_type="class",
            start_line=start_line,
            end_line=end_line,
            content=class_content,
            file_path=file_path
        )
        
        # 提取类文档字符串
        if (node.body and isinstance(node.body[0], ast.Expr) and 
            isinstance(node.body[0].value, ast.Constant) and 
            isinstance(node.body[0].value.value, str)):
            element.docstring = node.body[0].value.value
        
        return element
    
    def _parse_variable(self, node: ast.Assign, content: str, file_path: str) -> Optional[CodeElement]:
        """解析变量定义"""
        if not node.targets:
            return None
        
        target = node.targets[0]
        if not isinstance(target, ast.Name):
            return None
        
        lines = content.split('\n')
        start_line = node.lineno
        end_line = node.end_lineno or start_line
        
        var_content = '\n'.join(lines[start_line-1:end_line])
        
        element = CodeElement(
            name=target.id,
            element_type="variable",
            start_line=start_line,
            end_line=end_line,
            content=var_content,
            file_path=file_path
        )
        
        return element
    
    def _calculate_complexity(self, node: ast.FunctionDef) -> int:
        """计算函数的圈复杂度"""
        complexity = 1  # 基础复杂度
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity


class CodeParser:
    """通用代码解析器"""
    
    def __init__(self):
        self.parsers = {
            "python": PythonParser(),
        }
        self.tree_sitter_parsers = {}
        self._init_tree_sitter()
    
    def _init_tree_sitter(self):
        """初始化Tree-sitter解析器"""
        try:
            # 这里需要预先编译Tree-sitter语言库
            # 实际项目中需要设置正确的库路径
            pass
        except Exception as e:
            logger.warning(f"Tree-sitter初始化失败: {e}")
    
    def parse_file(self, file_path: str) -> List[CodeElement]:
        """解析代码文件"""
        language = self._detect_language(file_path)
        
        if language in self.parsers:
            return self.parsers[language].parse_file(file_path)
        else:
            # 使用通用解析器
            return self._parse_generic(file_path, language)
    
    def _detect_language(self, file_path: str) -> str:
        """检测文件语言"""
        ext = Path(file_path).suffix.lower()
        
        for language, extensions in LANGUAGE_EXTENSIONS.items():
            if ext in extensions:
                return language
        
        return "unknown"
    
    def _parse_generic(self, file_path: str, language: str) -> List[CodeElement]:
        """通用文件解析"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 简单的基于行的解析
            lines = content.split('\n')
            elements = []
            
            # 创建一个包含整个文件的元素
            element = CodeElement(
                name=Path(file_path).name,
                element_type="file",
                start_line=1,
                end_line=len(lines),
                content=content,
                file_path=file_path
            )
            elements.append(element)
            
            return elements
            
        except Exception as e:
            logger.error(f"解析文件失败 {file_path}: {e}")
            return []
    
    def get_file_summary(self, file_path: str) -> Dict[str, Any]:
        """获取文件摘要信息"""
        elements = self.parse_file(file_path)
        
        summary = {
            "file_path": file_path,
            "language": self._detect_language(file_path),
            "total_elements": len(elements),
            "functions": len([e for e in elements if e.element_type == "function"]),
            "classes": len([e for e in elements if e.element_type == "class"]),
            "variables": len([e for e in elements if e.element_type == "variable"]),
            "total_lines": 0,
            "complexity": 0,
        }
        
        if elements:
            summary["total_lines"] = max(e.end_line for e in elements)
            summary["complexity"] = sum(e.complexity for e in elements)
        
        return summary
