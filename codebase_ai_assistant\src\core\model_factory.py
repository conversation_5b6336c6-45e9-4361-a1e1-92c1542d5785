"""
模型工厂模块
根据配置创建不同的LLM和Embedding模型实例
"""
import logging
from typing import Union, Any
from langchain.chat_models import ChatOpenAI
from langchain.embeddings import OpenAIEmbeddings
from langchain.schema import BaseLanguageModel
from langchain.embeddings.base import Embeddings

try:
    from zhipuai import ZhipuAI
    ZHIPUAI_AVAILABLE = True
except ImportError:
    ZHIPUAI_AVAILABLE = False
    ZhipuAI = None

from .config import settings

logger = logging.getLogger(__name__)


class GLMChatModel:
    """GLM聊天模型包装器，兼容LangChain接口"""
    
    def __init__(self, api_key: str, model: str = "glm-4", temperature: float = 0.1):
        if not ZHIPUAI_AVAILABLE:
            raise ImportError("zhipuai package is not installed. Please install it with: pip install zhipuai")
        
        self.client = ZhipuAI(api_key=api_key)
        self.model = model
        self.temperature = temperature
    
    def invoke(self, messages, **kwargs):
        """调用GLM模型"""
        try:
            # 转换LangChain消息格式到GLM格式
            glm_messages = self._convert_messages(messages)
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=glm_messages,
                temperature=self.temperature,
                **kwargs
            )
            
            # 返回兼容LangChain的响应格式
            return GLMResponse(response.choices[0].message.content)
            
        except Exception as e:
            logger.error(f"GLM API调用失败: {e}")
            raise
    
    def _convert_messages(self, messages):
        """转换消息格式"""
        glm_messages = []
        
        for message in messages:
            if hasattr(message, 'content'):
                # LangChain消息对象
                if hasattr(message, 'type'):
                    role = message.type
                else:
                    role = "user"
                content = message.content
            elif isinstance(message, dict):
                # 字典格式消息
                role = message.get("role", "user")
                content = message.get("content", "")
            else:
                # 字符串消息
                role = "user"
                content = str(message)
            
            # 映射角色名称
            if role == "human":
                role = "user"
            elif role == "ai":
                role = "assistant"
            
            glm_messages.append({
                "role": role,
                "content": content
            })
        
        return glm_messages


class GLMResponse:
    """GLM响应包装器"""
    
    def __init__(self, content: str):
        self.content = content


class GLMEmbeddings:
    """GLM嵌入模型包装器"""
    
    def __init__(self, api_key: str, model: str = "embedding-2"):
        if not ZHIPUAI_AVAILABLE:
            raise ImportError("zhipuai package is not installed. Please install it with: pip install zhipuai")
        
        self.client = ZhipuAI(api_key=api_key)
        self.model = model
    
    def embed_documents(self, texts):
        """嵌入文档列表"""
        try:
            embeddings = []
            for text in texts:
                response = self.client.embeddings.create(
                    model=self.model,
                    input=text
                )
                embeddings.append(response.data[0].embedding)
            return embeddings
        except Exception as e:
            logger.error(f"GLM嵌入API调用失败: {e}")
            raise
    
    def embed_query(self, text):
        """嵌入单个查询"""
        try:
            response = self.client.embeddings.create(
                model=self.model,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"GLM嵌入API调用失败: {e}")
            raise


class ModelFactory:
    """模型工厂类"""
    
    @staticmethod
    def create_chat_model(temperature: float = 0.1, **kwargs) -> Union[ChatOpenAI, GLMChatModel]:
        """创建聊天模型"""
        provider = settings.model_provider.lower()
        
        if provider == "glm":
            if not settings.glm_api_key:
                raise ValueError("GLM API key is not configured. Please set GLM_API_KEY environment variable.")
            
            return GLMChatModel(
                api_key=settings.glm_api_key,
                model=settings.glm_model,
                temperature=temperature
            )
        
        elif provider == "openai":
            if not settings.openai_api_key:
                raise ValueError("OpenAI API key is not configured. Please set OPENAI_API_KEY environment variable.")
            
            return ChatOpenAI(
                model=settings.openai_model,
                temperature=temperature,
                openai_api_key=settings.openai_api_key,
                **kwargs
            )
        
        else:
            raise ValueError(f"Unsupported model provider: {provider}")
    
    @staticmethod
    def create_embeddings() -> Union[OpenAIEmbeddings, GLMEmbeddings]:
        """创建嵌入模型"""
        provider = settings.model_provider.lower()
        
        if provider == "glm":
            if not settings.glm_api_key:
                raise ValueError("GLM API key is not configured. Please set GLM_API_KEY environment variable.")
            
            return GLMEmbeddings(
                api_key=settings.glm_api_key,
                model=settings.glm_embedding_model
            )
        
        elif provider == "openai":
            if not settings.openai_api_key:
                raise ValueError("OpenAI API key is not configured. Please set OPENAI_API_KEY environment variable.")
            
            return OpenAIEmbeddings(
                model=settings.openai_embedding_model,
                openai_api_key=settings.openai_api_key
            )
        
        else:
            raise ValueError(f"Unsupported model provider: {provider}")
    
    @staticmethod
    def get_current_provider() -> str:
        """获取当前模型提供商"""
        return settings.model_provider
    
    @staticmethod
    def get_current_model_info() -> dict:
        """获取当前模型信息"""
        return {
            "provider": settings.model_provider,
            "chat_model": settings.current_model,
            "embedding_model": settings.current_embedding_model,
            "api_key_configured": bool(settings.current_api_key)
        }
