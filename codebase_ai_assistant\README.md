# 智能代码库助手 (CodeBase AI Assistant)

一个基于LangChain的智能代码分析和问答系统，能够理解你的代码库并提供智能帮助。

## 🎯 功能特性

- 📚 **代码库索引**: 自动分析和索引整个代码库
- 🤖 **智能问答**: 回答关于代码的任何问题  
- 🔍 **代码搜索**: 语义化搜索相关代码片段
- 📝 **文档生成**: 自动生成API文档和代码说明
- 🧪 **测试生成**: 为函数生成单元测试
- 🔧 **重构建议**: 提供代码优化建议
- 🐛 **Bug分析**: 分析潜在问题并提供修复建议

## 🛠️ 技术栈

- **LangChain**: 核心AI框架
- **ChromaDB**: 向量数据库
- **OpenAI GPT**: 大语言模型
- **Tree-sitter**: 代码解析
- **Streamlit**: Web界面
- **AST**: Python抽象语法树

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

## 🚀 快速开始

1. 设置环境变量
```bash
export OPENAI_API_KEY="your-api-key"
```

2. 索引代码库
```bash
python scripts/index_codebase.py --path /path/to/your/codebase
```

3. 启动Web界面
```bash
streamlit run app.py
```

## 📖 使用示例

### 基本问答
```
Q: "这个项目的主要功能是什么？"
A: 根据README.md和主要模块分析，这个项目是一个...

Q: "如何使用UserManager类？"
A: UserManager类位于src/models/user.py，主要用于...
```

### 代码生成
```
Q: "为calculate_total函数生成单元测试"
A: 基于函数签名和逻辑，生成以下测试代码...
```

## 🏗️ 项目架构

```
codebase_ai_assistant/
├── src/
│   ├── core/           # 核心功能
│   ├── agents/         # AI代理
│   ├── tools/          # 工具集
│   ├── parsers/        # 代码解析器
│   └── storage/        # 数据存储
├── web/                # Web界面
├── scripts/            # 脚本工具
├── tests/              # 测试文件
└── docs/               # 文档
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
