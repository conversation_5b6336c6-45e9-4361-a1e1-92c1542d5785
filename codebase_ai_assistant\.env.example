# 模型提供商配置 (openai 或 glm)
MODEL_PROVIDER=glm

# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002

# GLM API配置
GLM_API_KEY=your_glm_api_key_here
GLM_MODEL=glm-4
GLM_EMBEDDING_MODEL=embedding-2

# 向量数据库配置
VECTOR_DB_TYPE=chromadb
VECTOR_DB_PATH=./data/vectordb

# 代码解析配置
SUPPORTED_LANGUAGES=python,javascript,java,cpp,go,rust
MAX_FILE_SIZE=1048576

# 索引配置
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# 搜索配置
SEARCH_K=5
SIMILARITY_THRESHOLD=0.7

# 缓存配置
CACHE_ENABLED=true
CACHE_TTL=3600

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=
