#!/usr/bin/env python3
"""
智能代码库助手 Web 应用
基于Streamlit的用户界面
"""
import os
import sys
from pathlib import Path
import streamlit as st
import logging
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.config import settings
from src.storage.vector_store import VectorStore
from src.agents.code_agent import CodeAgent

# 配置页面
st.set_page_config(
    page_title="智能代码库助手",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@st.cache_resource
def initialize_components():
    """初始化组件（使用缓存避免重复初始化）"""
    try:
        vector_store = VectorStore()
        agent = CodeAgent(vector_store)
        return vector_store, agent
    except Exception as e:
        st.error(f"初始化失败: {e}")
        return None, None


def main():
    """主应用"""
    st.title("🤖 智能代码库助手")
    st.markdown("---")
    
    # 初始化组件
    vector_store, agent = initialize_components()
    
    if vector_store is None or agent is None:
        st.error("系统初始化失败，请检查配置")
        return
    
    # 侧边栏
    with st.sidebar:
        st.header("📊 系统状态")
        
        # 显示向量存储统计
        stats = vector_store.get_statistics()
        if stats:
            st.metric("总文档数", stats.get("total_documents", 0))
            
            # 语言分布
            lang_dist = stats.get("language_distribution", {})
            if lang_dist:
                st.subheader("语言分布")
                for lang, count in lang_dist.items():
                    st.text(f"{lang}: {count}")
            
            # 元素类型分布
            type_dist = stats.get("element_type_distribution", {})
            if type_dist:
                st.subheader("元素类型分布")
                for elem_type, count in type_dist.items():
                    st.text(f"{elem_type}: {count}")
        else:
            st.warning("暂无数据，请先索引代码库")
        
        st.markdown("---")
        
        # 工具栏
        st.header("🛠️ 工具")
        
        if st.button("🗑️ 清空聊天历史"):
            agent.clear_history()
            st.success("聊天历史已清空")
            st.experimental_rerun()
        
        if st.button("📊 刷新统计"):
            st.experimental_rerun()
    
    # 主界面
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("💬 智能问答")
        
        # 聊天界面
        if "messages" not in st.session_state:
            st.session_state.messages = []
        
        # 显示聊天历史
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                st.markdown(message["content"])
        
        # 用户输入
        if prompt := st.chat_input("请输入您的问题..."):
            # 添加用户消息
            st.session_state.messages.append({"role": "user", "content": prompt})
            with st.chat_message("user"):
                st.markdown(prompt)
            
            # 获取AI回复
            with st.chat_message("assistant"):
                with st.spinner("思考中..."):
                    try:
                        response = agent.chat(prompt)
                        st.markdown(response)
                        st.session_state.messages.append({"role": "assistant", "content": response})
                    except Exception as e:
                        error_msg = f"抱歉，处理您的请求时出现错误: {str(e)}"
                        st.error(error_msg)
                        st.session_state.messages.append({"role": "assistant", "content": error_msg})
    
    with col2:
        st.header("🔍 快速操作")
        
        # 预设问题
        st.subheader("常用问题")
        quick_questions = [
            "这个项目的主要功能是什么？",
            "有哪些主要的类和函数？",
            "代码的整体架构是怎样的？",
            "有什么潜在的问题需要注意？",
            "如何改进代码质量？",
        ]
        
        for question in quick_questions:
            if st.button(question, key=f"quick_{hash(question)}"):
                # 模拟用户输入
                st.session_state.messages.append({"role": "user", "content": question})
                
                # 获取回复
                with st.spinner("处理中..."):
                    try:
                        response = agent.chat(question)
                        st.session_state.messages.append({"role": "assistant", "content": response})
                        st.experimental_rerun()
                    except Exception as e:
                        error_msg = f"处理失败: {str(e)}"
                        st.session_state.messages.append({"role": "assistant", "content": error_msg})
                        st.experimental_rerun()
        
        st.markdown("---")
        
        # 代码搜索
        st.subheader("🔍 代码搜索")
        search_query = st.text_input("搜索代码片段:")
        search_type = st.selectbox("搜索类型", ["相似性搜索", "按文件", "按语言", "按类型"])
        
        if st.button("搜索") and search_query:
            with st.spinner("搜索中..."):
                try:
                    if search_type == "相似性搜索":
                        results = vector_store.search_similar_code(search_query, k=5)
                    elif search_type == "按文件":
                        results = vector_store.search_by_file(search_query, k=5)
                    elif search_type == "按语言":
                        results = vector_store.search_by_language(search_query, k=5)
                    elif search_type == "按类型":
                        results = vector_store.search_by_element_type(search_query, k=5)
                    
                    if results:
                        st.success(f"找到 {len(results)} 个结果")
                        for i, doc in enumerate(results, 1):
                            with st.expander(f"结果 {i}: {doc.element_name} ({doc.element_type})"):
                                st.text(f"文件: {doc.file_path}")
                                st.text(f"行数: {doc.start_line}-{doc.end_line}")
                                st.code(doc.page_content, language=doc.language)
                    else:
                        st.warning("未找到相关结果")
                        
                except Exception as e:
                    st.error(f"搜索失败: {str(e)}")
        
        st.markdown("---")
        
        # 文件上传（用于临时分析）
        st.subheader("📁 文件分析")
        uploaded_file = st.file_uploader("上传代码文件进行分析", type=['py', 'js', 'java', 'cpp', 'go'])
        
        if uploaded_file is not None:
            # 保存临时文件
            temp_path = f"/tmp/{uploaded_file.name}"
            with open(temp_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            
            # 分析文件
            with st.spinner("分析中..."):
                try:
                    from src.parsers.code_parser import CodeParser
                    parser = CodeParser()
                    elements = parser.parse_file(temp_path)
                    
                    if elements:
                        st.success(f"分析完成，找到 {len(elements)} 个代码元素")
                        
                        for element in elements:
                            with st.expander(f"{element.element_type}: {element.name}"):
                                st.text(f"行数: {element.start_line}-{element.end_line}")
                                if element.docstring:
                                    st.text(f"文档: {element.docstring}")
                                if element.parameters:
                                    st.text(f"参数: {', '.join(element.parameters)}")
                                st.code(element.content)
                    else:
                        st.warning("未能解析文件")
                        
                except Exception as e:
                    st.error(f"分析失败: {str(e)}")
                finally:
                    # 清理临时文件
                    if os.path.exists(temp_path):
                        os.remove(temp_path)


def show_help():
    """显示帮助信息"""
    st.header("📖 使用帮助")
    
    st.markdown("""
    ## 功能介绍
    
    ### 💬 智能问答
    - 询问代码库的任何问题
    - 获取代码解释和分析
    - 请求代码优化建议
    
    ### 🔍 代码搜索
    - 语义化搜索相关代码
    - 按文件、语言、类型筛选
    - 快速定位目标代码
    
    ### 🛠️ 代码工具
    - 生成单元测试
    - 生成API文档
    - 代码质量分析
    
    ## 使用示例
    
    ### 问答示例
    ```
    Q: "UserManager类有什么功能？"
    Q: "如何使用calculate_total函数？"
    Q: "这个项目的主要架构是什么？"
    Q: "为login函数生成单元测试"
    ```
    
    ### 搜索示例
    ```
    - 搜索: "用户认证"
    - 按文件: "user.py"
    - 按语言: "python"
    - 按类型: "function"
    ```
    """)


if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv("OPENAI_API_KEY"):
        st.error("请设置 OPENAI_API_KEY 环境变量")
        st.stop()
    
    # 创建标签页
    tab1, tab2 = st.tabs(["🏠 主页", "📖 帮助"])
    
    with tab1:
        main()
    
    with tab2:
        show_help()
