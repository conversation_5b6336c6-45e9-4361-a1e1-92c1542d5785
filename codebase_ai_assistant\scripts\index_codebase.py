#!/usr/bin/env python3
"""
代码库索引脚本
扫描指定目录的代码文件，解析并存储到向量数据库
"""
import os
import sys
import argparse
import logging
from pathlib import Path
from typing import List, Set
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.config import settings, LANGUAGE_EXTENSIONS, IGNORE_PATTERNS
from src.parsers.code_parser import CodeParser
from src.storage.vector_store import VectorStore

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CodebaseIndexer:
    """代码库索引器"""
    
    def __init__(self, target_path: str):
        self.target_path = Path(target_path)
        self.parser = CodeParser()
        self.vector_store = VectorStore()
        
        # 统计信息
        self.stats = {
            "total_files": 0,
            "processed_files": 0,
            "skipped_files": 0,
            "total_elements": 0,
            "errors": 0,
            "languages": {},
        }
    
    def index_codebase(self, force_reindex: bool = False) -> bool:
        """索引整个代码库"""
        logger.info(f"开始索引代码库: {self.target_path}")
        
        if not self.target_path.exists():
            logger.error(f"目标路径不存在: {self.target_path}")
            return False
        
        if force_reindex:
            logger.info("强制重新索引，清空现有数据...")
            self.vector_store.clear_all()
        
        start_time = time.time()
        
        try:
            # 扫描文件
            code_files = self._scan_code_files()
            self.stats["total_files"] = len(code_files)
            
            logger.info(f"找到 {len(code_files)} 个代码文件")
            
            # 处理文件
            for file_path in code_files:
                self._process_file(file_path)
            
            # 输出统计信息
            elapsed_time = time.time() - start_time
            self._print_statistics(elapsed_time)
            
            return True
            
        except Exception as e:
            logger.error(f"索引过程中出现错误: {e}")
            return False
    
    def _scan_code_files(self) -> List[Path]:
        """扫描代码文件"""
        code_files = []
        
        # 获取支持的文件扩展名
        supported_extensions = set()
        for extensions in LANGUAGE_EXTENSIONS.values():
            supported_extensions.update(extensions)
        
        # 递归扫描目录
        for root, dirs, files in os.walk(self.target_path):
            # 过滤忽略的目录
            dirs[:] = [d for d in dirs if not self._should_ignore(d)]
            
            for file in files:
                file_path = Path(root) / file
                
                # 检查文件扩展名
                if file_path.suffix.lower() in supported_extensions:
                    # 检查文件大小
                    if file_path.stat().st_size <= settings.max_file_size:
                        # 检查是否应该忽略
                        if not self._should_ignore(file):
                            code_files.append(file_path)
                    else:
                        logger.warning(f"文件过大，跳过: {file_path}")
                        self.stats["skipped_files"] += 1
        
        return code_files
    
    def _should_ignore(self, name: str) -> bool:
        """检查是否应该忽略文件/目录"""
        for pattern in IGNORE_PATTERNS:
            if pattern.startswith("*"):
                if name.endswith(pattern[1:]):
                    return True
            elif pattern.endswith("*"):
                if name.startswith(pattern[:-1]):
                    return True
            else:
                if pattern == name:
                    return True
        return False
    
    def _process_file(self, file_path: Path) -> bool:
        """处理单个文件"""
        try:
            logger.debug(f"处理文件: {file_path}")
            
            # 解析文件
            elements = self.parser.parse_file(str(file_path))
            
            if not elements:
                logger.warning(f"未能解析文件: {file_path}")
                self.stats["skipped_files"] += 1
                return False
            
            # 添加到向量存储
            ids = self.vector_store.add_code_elements(elements)
            
            if ids:
                self.stats["processed_files"] += 1
                self.stats["total_elements"] += len(elements)
                
                # 统计语言
                language = self.parser._detect_language(str(file_path))
                self.stats["languages"][language] = self.stats["languages"].get(language, 0) + 1
                
                logger.debug(f"成功处理文件: {file_path}, 元素数量: {len(elements)}")
                return True
            else:
                logger.error(f"添加文件到向量存储失败: {file_path}")
                self.stats["errors"] += 1
                return False
                
        except Exception as e:
            logger.error(f"处理文件失败 {file_path}: {e}")
            self.stats["errors"] += 1
            return False
    
    def _print_statistics(self, elapsed_time: float):
        """打印统计信息"""
        print("\n" + "="*50)
        print("索引完成统计")
        print("="*50)
        print(f"总文件数: {self.stats['total_files']}")
        print(f"成功处理: {self.stats['processed_files']}")
        print(f"跳过文件: {self.stats['skipped_files']}")
        print(f"错误文件: {self.stats['errors']}")
        print(f"总代码元素: {self.stats['total_elements']}")
        print(f"处理时间: {elapsed_time:.2f} 秒")
        
        if self.stats["languages"]:
            print("\n语言分布:")
            for language, count in sorted(self.stats["languages"].items()):
                print(f"  {language}: {count} 个文件")
        
        # 获取向量存储统计
        vector_stats = self.vector_store.get_statistics()
        if vector_stats:
            print(f"\n向量存储统计:")
            print(f"  总文档数: {vector_stats.get('total_documents', 0)}")
        
        print("="*50)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="代码库索引工具")
    parser.add_argument("--path", "-p", required=True, help="要索引的代码库路径")
    parser.add_argument("--force", "-f", action="store_true", help="强制重新索引")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 验证路径
    target_path = Path(args.path).resolve()
    if not target_path.exists():
        print(f"错误: 路径不存在 - {target_path}")
        sys.exit(1)
    
    if not target_path.is_dir():
        print(f"错误: 路径不是目录 - {target_path}")
        sys.exit(1)
    
    # 创建索引器并执行索引
    indexer = CodebaseIndexer(str(target_path))
    
    try:
        success = indexer.index_codebase(force_reindex=args.force)
        
        if success:
            print("\n✅ 索引完成!")
            sys.exit(0)
        else:
            print("\n❌ 索引失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  索引被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 索引过程中出现未预期的错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
