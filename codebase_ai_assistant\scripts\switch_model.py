#!/usr/bin/env python3
"""
模型切换脚本
快速切换不同的模型提供商
"""
import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.config import settings
from src.core.model_factory import ModelFactory


def switch_to_openai(api_key: str = None):
    """切换到OpenAI"""
    print("🔄 切换到 OpenAI...")
    
    # 更新环境变量
    os.environ["MODEL_PROVIDER"] = "openai"
    if api_key:
        os.environ["OPENAI_API_KEY"] = api_key
    
    # 测试连接
    try:
        model_info = ModelFactory.get_current_model_info()
        print(f"✅ 成功切换到 OpenAI")
        print(f"   聊天模型: {model_info['chat_model']}")
        print(f"   嵌入模型: {model_info['embedding_model']}")
        print(f"   API密钥: {'已配置' if model_info['api_key_configured'] else '未配置'}")
        
        if model_info['api_key_configured']:
            # 测试模型调用
            llm = ModelFactory.create_chat_model()
            response = llm.invoke([{"role": "user", "content": "Hello"}])
            print(f"   测试调用: 成功")
        
    except Exception as e:
        print(f"❌ 切换失败: {e}")
        return False
    
    return True


def switch_to_glm(api_key: str = None):
    """切换到GLM"""
    print("🔄 切换到 GLM...")
    
    # 更新环境变量
    os.environ["MODEL_PROVIDER"] = "glm"
    if api_key:
        os.environ["GLM_API_KEY"] = api_key
    
    # 测试连接
    try:
        model_info = ModelFactory.get_current_model_info()
        print(f"✅ 成功切换到 GLM")
        print(f"   聊天模型: {model_info['chat_model']}")
        print(f"   嵌入模型: {model_info['embedding_model']}")
        print(f"   API密钥: {'已配置' if model_info['api_key_configured'] else '未配置'}")
        
        if model_info['api_key_configured']:
            # 测试模型调用
            llm = ModelFactory.create_chat_model()
            response = llm.invoke([{"role": "user", "content": "你好"}])
            print(f"   测试调用: 成功")
        
    except Exception as e:
        print(f"❌ 切换失败: {e}")
        return False
    
    return True


def show_current_config():
    """显示当前配置"""
    print("📋 当前模型配置:")
    print("-" * 30)
    
    try:
        model_info = ModelFactory.get_current_model_info()
        print(f"提供商: {model_info['provider']}")
        print(f"聊天模型: {model_info['chat_model']}")
        print(f"嵌入模型: {model_info['embedding_model']}")
        print(f"API密钥: {'已配置' if model_info['api_key_configured'] else '未配置'}")
        
        # 显示环境变量
        print("\n🔧 环境变量:")
        print(f"MODEL_PROVIDER: {os.getenv('MODEL_PROVIDER', '未设置')}")
        print(f"OPENAI_API_KEY: {'已设置' if os.getenv('OPENAI_API_KEY') else '未设置'}")
        print(f"GLM_API_KEY: {'已设置' if os.getenv('GLM_API_KEY') else '未设置'}")
        
    except Exception as e:
        print(f"❌ 获取配置失败: {e}")


def update_env_file(provider: str, api_key: str = None):
    """更新.env文件"""
    env_file = project_root / ".env"
    
    if not env_file.exists():
        print("⚠️  .env文件不存在，创建新文件...")
        env_file.touch()
    
    # 读取现有内容
    lines = []
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    
    # 更新配置
    updated_lines = []
    provider_updated = False
    api_key_updated = False
    
    for line in lines:
        if line.startswith("MODEL_PROVIDER="):
            updated_lines.append(f"MODEL_PROVIDER={provider}\n")
            provider_updated = True
        elif provider == "openai" and api_key and line.startswith("OPENAI_API_KEY="):
            updated_lines.append(f"OPENAI_API_KEY={api_key}\n")
            api_key_updated = True
        elif provider == "glm" and api_key and line.startswith("GLM_API_KEY="):
            updated_lines.append(f"GLM_API_KEY={api_key}\n")
            api_key_updated = True
        else:
            updated_lines.append(line)
    
    # 添加缺失的配置
    if not provider_updated:
        updated_lines.append(f"MODEL_PROVIDER={provider}\n")
    
    if api_key and not api_key_updated:
        if provider == "openai":
            updated_lines.append(f"OPENAI_API_KEY={api_key}\n")
        elif provider == "glm":
            updated_lines.append(f"GLM_API_KEY={api_key}\n")
    
    # 写入文件
    with open(env_file, 'w', encoding='utf-8') as f:
        f.writelines(updated_lines)
    
    print(f"✅ 已更新 .env 文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="模型切换工具")
    parser.add_argument("--provider", "-p", choices=["openai", "glm"], help="模型提供商")
    parser.add_argument("--api-key", "-k", help="API密钥")
    parser.add_argument("--show", "-s", action="store_true", help="显示当前配置")
    parser.add_argument("--update-env", "-u", action="store_true", help="更新.env文件")
    
    args = parser.parse_args()
    
    if args.show:
        show_current_config()
        return
    
    if not args.provider:
        print("❌ 请指定模型提供商 (--provider openai 或 --provider glm)")
        show_current_config()
        return
    
    # 切换模型
    success = False
    if args.provider == "openai":
        success = switch_to_openai(args.api_key)
    elif args.provider == "glm":
        success = switch_to_glm(args.api_key)
    
    # 更新.env文件
    if success and args.update_env:
        update_env_file(args.provider, args.api_key)
    
    if success:
        print("\n🎉 模型切换完成!")
        print("💡 提示: 重启应用以使配置生效")
    else:
        print("\n❌ 模型切换失败!")


if __name__ == "__main__":
    main()
