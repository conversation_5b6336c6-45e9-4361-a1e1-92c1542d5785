"""
代码分析工具模块
提供代码分析、测试生成、文档生成等功能
"""
import re
import ast
from typing import List, Dict, Any, Optional
import logging

from langchain.prompts import PromptTemplate
from langchain.schema import HumanMessage

from ..core.config import settings
from ..core.model_factory import ModelFactory
from ..storage.vector_store import CodeDocument

logger = logging.getLogger(__name__)


class CodeAnalyzer:
    """代码分析器"""
    
    def __init__(self):
        self.llm = ModelFactory.create_chat_model(temperature=0.1)
    
    def analyze_file_structure(self, docs: List[CodeDocument]) -> Dict[str, Any]:
        """分析文件结构"""
        analysis = {
            "language": "unknown",
            "total_lines": 0,
            "functions": 0,
            "classes": 0,
            "variables": 0,
            "complexity": 0,
            "functions_list": [],
            "classes_list": [],
            "imports": [],
            "dependencies": [],
        }
        
        for doc in docs:
            # 更新基本统计
            if doc.language != "unknown":
                analysis["language"] = doc.language
            
            analysis["total_lines"] = max(analysis["total_lines"], doc.end_line)
            analysis["complexity"] += doc.complexity
            
            # 统计元素类型
            if doc.element_type == "function":
                analysis["functions"] += 1
                analysis["functions_list"].append(doc.element_name)
            elif doc.element_type == "class":
                analysis["classes"] += 1
                analysis["classes_list"].append(doc.element_name)
            elif doc.element_type == "variable":
                analysis["variables"] += 1
        
        # 分析导入和依赖
        for doc in docs:
            imports = self._extract_imports(doc.page_content, doc.language)
            analysis["imports"].extend(imports)
        
        # 去重
        analysis["imports"] = list(set(analysis["imports"]))
        analysis["functions_list"] = list(set(analysis["functions_list"]))
        analysis["classes_list"] = list(set(analysis["classes_list"]))
        
        return analysis
    
    def _extract_imports(self, content: str, language: str) -> List[str]:
        """提取导入语句"""
        imports = []
        
        if language == "python":
            # 使用正则表达式提取Python导入
            import_patterns = [
                r'import\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)',
                r'from\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\s+import',
            ]
            
            for pattern in import_patterns:
                matches = re.findall(pattern, content)
                imports.extend(matches)
        
        elif language == "javascript":
            # JavaScript导入
            js_patterns = [
                r'import.*from\s+[\'"]([^\'"]+)[\'"]',
                r'require\([\'"]([^\'"]+)[\'"]\)',
            ]
            
            for pattern in js_patterns:
                matches = re.findall(pattern, content)
                imports.extend(matches)
        
        return imports
    
    def analyze_code_quality(self, content: str, language: str) -> Dict[str, Any]:
        """分析代码质量"""
        quality_analysis = {
            "complexity_score": 0,
            "maintainability": "unknown",
            "issues": [],
            "suggestions": [],
        }
        
        if language == "python":
            quality_analysis = self._analyze_python_quality(content)
        
        return quality_analysis
    
    def _analyze_python_quality(self, content: str) -> Dict[str, Any]:
        """分析Python代码质量"""
        issues = []
        suggestions = []
        complexity_score = 0
        
        try:
            tree = ast.parse(content)
            
            # 分析函数复杂度
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_complexity = self._calculate_function_complexity(node)
                    complexity_score += func_complexity
                    
                    if func_complexity > 10:
                        issues.append(f"函数 {node.name} 复杂度过高 ({func_complexity})")
                        suggestions.append(f"考虑重构函数 {node.name} 以降低复杂度")
                    
                    # 检查函数长度
                    func_lines = node.end_lineno - node.lineno
                    if func_lines > 50:
                        issues.append(f"函数 {node.name} 过长 ({func_lines} 行)")
                        suggestions.append(f"考虑拆分函数 {node.name}")
                    
                    # 检查参数数量
                    param_count = len(node.args.args)
                    if param_count > 5:
                        issues.append(f"函数 {node.name} 参数过多 ({param_count} 个)")
                        suggestions.append(f"考虑使用对象或字典来减少 {node.name} 的参数数量")
        
        except SyntaxError:
            issues.append("代码存在语法错误")
        
        # 确定可维护性等级
        if complexity_score < 10:
            maintainability = "excellent"
        elif complexity_score < 20:
            maintainability = "good"
        elif complexity_score < 40:
            maintainability = "moderate"
        else:
            maintainability = "poor"
        
        return {
            "complexity_score": complexity_score,
            "maintainability": maintainability,
            "issues": issues,
            "suggestions": suggestions,
        }
    
    def _calculate_function_complexity(self, node: ast.FunctionDef) -> int:
        """计算函数复杂度"""
        complexity = 1
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity


class TestGenerator:
    """测试代码生成器"""
    
    def __init__(self):
        self.llm = ModelFactory.create_chat_model(temperature=0.2)
    
    def generate_test(self, element_name: str, element_type: str, 
                     code_content: str, language: str) -> str:
        """生成测试代码"""
        
        if language == "python":
            return self._generate_python_test(element_name, element_type, code_content)
        elif language == "javascript":
            return self._generate_javascript_test(element_name, element_type, code_content)
        else:
            return self._generate_generic_test(element_name, element_type, code_content, language)
    
    def _generate_python_test(self, element_name: str, element_type: str, code_content: str) -> str:
        """生成Python测试代码"""
        prompt = PromptTemplate(
            input_variables=["element_name", "element_type", "code_content"],
            template="""
为以下{element_type}生成完整的Python单元测试代码：

{element_type}名称: {element_name}
代码内容:
{code_content}

请生成包含以下内容的测试代码：
1. 导入必要的模块（unittest, mock等）
2. 测试类定义
3. 多个测试方法，覆盖正常情况、边界情况和异常情况
4. 适当的断言
5. 如果需要，包含mock和fixture

测试代码应该遵循Python测试最佳实践。
"""
        )
        
        formatted_prompt = prompt.format(
            element_name=element_name,
            element_type=element_type,
            code_content=code_content
        )
        
        try:
            response = self.llm.invoke([HumanMessage(content=formatted_prompt)])
            return response.content
        except Exception as e:
            logger.error(f"生成Python测试失败: {e}")
            return f"# 测试生成失败: {str(e)}"
    
    def _generate_javascript_test(self, element_name: str, element_type: str, code_content: str) -> str:
        """生成JavaScript测试代码"""
        prompt = PromptTemplate(
            input_variables=["element_name", "element_type", "code_content"],
            template="""
为以下{element_type}生成完整的JavaScript测试代码：

{element_type}名称: {element_name}
代码内容:
{code_content}

请生成包含以下内容的测试代码：
1. 使用Jest或Mocha框架
2. 导入被测试的模块
3. describe和it块的合理组织
4. 多个测试用例，覆盖各种场景
5. 适当的expect断言
6. 如果需要，包含mock和spy

测试代码应该遵循JavaScript测试最佳实践。
"""
        )
        
        formatted_prompt = prompt.format(
            element_name=element_name,
            element_type=element_type,
            code_content=code_content
        )
        
        try:
            response = self.llm.invoke([HumanMessage(content=formatted_prompt)])
            return response.content
        except Exception as e:
            logger.error(f"生成JavaScript测试失败: {e}")
            return f"// 测试生成失败: {str(e)}"
    
    def _generate_generic_test(self, element_name: str, element_type: str, 
                              code_content: str, language: str) -> str:
        """生成通用测试代码"""
        prompt = PromptTemplate(
            input_variables=["element_name", "element_type", "code_content", "language"],
            template="""
为以下{language}语言的{element_type}生成测试代码：

{element_type}名称: {element_name}
代码内容:
{code_content}

请根据{language}语言的测试框架和最佳实践生成测试代码。
包含多个测试用例，覆盖正常情况、边界情况和异常情况。
"""
        )
        
        formatted_prompt = prompt.format(
            element_name=element_name,
            element_type=element_type,
            code_content=code_content,
            language=language
        )
        
        try:
            response = self.llm.invoke([HumanMessage(content=formatted_prompt)])
            return response.content
        except Exception as e:
            logger.error(f"生成{language}测试失败: {e}")
            return f"// 测试生成失败: {str(e)}"


class DocumentGenerator:
    """文档生成器"""
    
    def __init__(self):
        self.llm = ModelFactory.create_chat_model(temperature=0.1)
    
    def generate_documentation(self, results: List[CodeDocument], doc_type: str = "api") -> str:
        """生成文档"""
        
        if doc_type == "api":
            return self._generate_api_docs(results)
        elif doc_type == "readme":
            return self._generate_readme(results)
        elif doc_type == "tutorial":
            return self._generate_tutorial(results)
        else:
            return self._generate_general_docs(results)
    
    def _generate_api_docs(self, results: List[CodeDocument]) -> str:
        """生成API文档"""
        code_content = "\n\n".join([doc.page_content for doc in results])
        
        prompt = PromptTemplate(
            input_variables=["code_content"],
            template="""
基于以下代码内容生成详细的API文档：

{code_content}

请生成包含以下内容的API文档：
1. 概述和用途说明
2. 每个函数/方法的详细说明
3. 参数说明（类型、默认值、描述）
4. 返回值说明
5. 使用示例
6. 注意事项和限制

文档应该使用Markdown格式，清晰易读。
"""
        )
        
        formatted_prompt = prompt.format(code_content=code_content)
        
        try:
            response = self.llm.invoke([HumanMessage(content=formatted_prompt)])
            return response.content
        except Exception as e:
            logger.error(f"生成API文档失败: {e}")
            return f"# 文档生成失败\n\n错误: {str(e)}"
    
    def _generate_readme(self, results: List[CodeDocument]) -> str:
        """生成README文档"""
        # 实现README生成逻辑
        pass
    
    def _generate_tutorial(self, results: List[CodeDocument]) -> str:
        """生成教程文档"""
        # 实现教程生成逻辑
        pass
    
    def _generate_general_docs(self, results: List[CodeDocument]) -> str:
        """生成通用文档"""
        # 实现通用文档生成逻辑
        pass
