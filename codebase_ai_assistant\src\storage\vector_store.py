"""
向量存储模块
管理代码片段的向量化存储和检索
"""
import os
import json
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import logging

import chromadb
from chromadb.config import Settings as ChromaSettings
from langchain.vectorstores import Chroma
from langchain.schema import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter

from ..core.config import settings
from ..core.model_factory import ModelFactory
from ..parsers.code_parser import CodeElement

logger = logging.getLogger(__name__)


class CodeDocument(Document):
    """代码文档类，扩展LangChain的Document"""
    
    def __init__(self, page_content: str, metadata: Dict[str, Any]):
        super().__init__(page_content=page_content, metadata=metadata)
        
        # 代码特有的元数据
        self.file_path = metadata.get("file_path", "")
        self.language = metadata.get("language", "")
        self.element_type = metadata.get("element_type", "")
        self.element_name = metadata.get("element_name", "")
        self.start_line = metadata.get("start_line", 0)
        self.end_line = metadata.get("end_line", 0)
        self.complexity = metadata.get("complexity", 0)


class VectorStore:
    """向量存储管理器"""
    
    def __init__(self):
        self.embeddings = ModelFactory.create_embeddings()
        
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.chunk_size,
            chunk_overlap=settings.chunk_overlap,
            separators=["\n\n", "\n", " ", ""]
        )
        
        self.vector_store: Optional[Chroma] = None
        self.client = None
        self._init_vector_store()
    
    def _init_vector_store(self):
        """初始化向量存储"""
        try:
            # 确保存储目录存在
            os.makedirs(settings.vector_db_path, exist_ok=True)
            
            # 初始化ChromaDB客户端
            self.client = chromadb.PersistentClient(
                path=settings.vector_db_path,
                settings=ChromaSettings(anonymized_telemetry=False)
            )
            
            # 初始化LangChain的Chroma向量存储
            self.vector_store = Chroma(
                client=self.client,
                collection_name="codebase",
                embedding_function=self.embeddings,
                persist_directory=settings.vector_db_path
            )
            
            logger.info(f"向量存储初始化成功: {settings.vector_db_path}")
            
        except Exception as e:
            logger.error(f"向量存储初始化失败: {e}")
            raise
    
    def add_code_elements(self, elements: List[CodeElement]) -> List[str]:
        """添加代码元素到向量存储"""
        documents = []
        
        for element in elements:
            # 创建文档内容
            content = self._create_document_content(element)
            
            # 创建元数据
            metadata = {
                "file_path": element.file_path,
                "language": self._detect_language(element.file_path),
                "element_type": element.element_type,
                "element_name": element.name,
                "start_line": element.start_line,
                "end_line": element.end_line,
                "complexity": element.complexity,
                "docstring": element.docstring or "",
                "parameters": json.dumps(element.parameters),
                "return_type": element.return_type or "",
                "content_hash": self._calculate_hash(element.content),
            }
            
            # 创建文档
            doc = CodeDocument(page_content=content, metadata=metadata)
            documents.append(doc)
        
        # 分割长文档
        split_docs = []
        for doc in documents:
            if len(doc.page_content) > settings.chunk_size:
                chunks = self.text_splitter.split_documents([doc])
                split_docs.extend(chunks)
            else:
                split_docs.append(doc)
        
        # 添加到向量存储
        try:
            ids = self.vector_store.add_documents(split_docs)
            logger.info(f"成功添加 {len(split_docs)} 个文档到向量存储")
            return ids
        except Exception as e:
            logger.error(f"添加文档到向量存储失败: {e}")
            return []
    
    def _create_document_content(self, element: CodeElement) -> str:
        """创建文档内容"""
        content_parts = []
        
        # 添加元素信息
        content_parts.append(f"文件: {element.file_path}")
        content_parts.append(f"类型: {element.element_type}")
        content_parts.append(f"名称: {element.name}")
        
        # 添加文档字符串
        if element.docstring:
            content_parts.append(f"文档: {element.docstring}")
        
        # 添加参数信息
        if element.parameters:
            content_parts.append(f"参数: {', '.join(element.parameters)}")
        
        # 添加返回类型
        if element.return_type:
            content_parts.append(f"返回类型: {element.return_type}")
        
        # 添加代码内容
        content_parts.append("代码:")
        content_parts.append(element.content)
        
        return "\n".join(content_parts)
    
    def search_similar_code(self, query: str, k: int = None, 
                           filter_dict: Dict[str, Any] = None) -> List[CodeDocument]:
        """搜索相似代码"""
        k = k or settings.search_k
        
        try:
            # 执行相似性搜索
            results = self.vector_store.similarity_search_with_score(
                query=query,
                k=k,
                filter=filter_dict
            )
            
            # 过滤低相似度结果
            filtered_results = []
            for doc, score in results:
                if score >= settings.similarity_threshold:
                    # 转换为CodeDocument
                    code_doc = CodeDocument(
                        page_content=doc.page_content,
                        metadata=doc.metadata
                    )
                    code_doc.similarity_score = score
                    filtered_results.append(code_doc)
            
            logger.info(f"搜索到 {len(filtered_results)} 个相关代码片段")
            return filtered_results
            
        except Exception as e:
            logger.error(f"代码搜索失败: {e}")
            return []
    
    def search_by_element_type(self, element_type: str, k: int = None) -> List[CodeDocument]:
        """按元素类型搜索"""
        filter_dict = {"element_type": element_type}
        return self.search_similar_code("", k=k, filter_dict=filter_dict)
    
    def search_by_file(self, file_path: str, k: int = None) -> List[CodeDocument]:
        """按文件路径搜索"""
        filter_dict = {"file_path": file_path}
        return self.search_similar_code("", k=k, filter_dict=filter_dict)
    
    def search_by_language(self, language: str, k: int = None) -> List[CodeDocument]:
        """按编程语言搜索"""
        filter_dict = {"language": language}
        return self.search_similar_code("", k=k, filter_dict=filter_dict)
    
    def update_code_element(self, element: CodeElement) -> bool:
        """更新代码元素"""
        try:
            # 先删除旧版本
            self.delete_by_hash(self._calculate_hash(element.content))
            
            # 添加新版本
            ids = self.add_code_elements([element])
            return len(ids) > 0
            
        except Exception as e:
            logger.error(f"更新代码元素失败: {e}")
            return False
    
    def delete_by_hash(self, content_hash: str) -> bool:
        """根据内容哈希删除文档"""
        try:
            # ChromaDB的删除操作
            collection = self.client.get_collection("codebase")
            collection.delete(where={"content_hash": content_hash})
            return True
            
        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return False
    
    def delete_by_file(self, file_path: str) -> bool:
        """删除指定文件的所有文档"""
        try:
            collection = self.client.get_collection("codebase")
            collection.delete(where={"file_path": file_path})
            logger.info(f"删除文件相关文档: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"删除文件文档失败: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取向量存储统计信息"""
        try:
            collection = self.client.get_collection("codebase")
            count = collection.count()
            
            # 获取语言分布
            results = collection.get(include=["metadatas"])
            language_counts = {}
            element_type_counts = {}
            
            for metadata in results.get("metadatas", []):
                lang = metadata.get("language", "unknown")
                element_type = metadata.get("element_type", "unknown")
                
                language_counts[lang] = language_counts.get(lang, 0) + 1
                element_type_counts[element_type] = element_type_counts.get(element_type, 0) + 1
            
            return {
                "total_documents": count,
                "language_distribution": language_counts,
                "element_type_distribution": element_type_counts,
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def _detect_language(self, file_path: str) -> str:
        """检测文件语言"""
        from ..core.config import LANGUAGE_EXTENSIONS
        
        ext = Path(file_path).suffix.lower()
        for language, extensions in LANGUAGE_EXTENSIONS.items():
            if ext in extensions:
                return language
        return "unknown"
    
    def _calculate_hash(self, content: str) -> str:
        """计算内容哈希"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def clear_all(self) -> bool:
        """清空所有数据"""
        try:
            self.client.delete_collection("codebase")
            self._init_vector_store()
            logger.info("向量存储已清空")
            return True
        except Exception as e:
            logger.error(f"清空向量存储失败: {e}")
            return False
