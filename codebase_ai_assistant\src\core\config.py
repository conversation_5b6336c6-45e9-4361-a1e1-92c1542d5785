"""
配置管理模块
"""
import os
from typing import List, Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """应用配置"""
    
    # API配置
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-4", env="OPENAI_MODEL")
    
    # 向量数据库配置
    vector_db_type: str = Field(default="chromadb", env="VECTOR_DB_TYPE")
    vector_db_path: str = Field(default="./data/vectordb", env="VECTOR_DB_PATH")
    embedding_model: str = Field(default="text-embedding-ada-002", env="EMBEDDING_MODEL")
    
    # 代码解析配置
    supported_languages: List[str] = Field(
        default=["python", "javascript", "java", "cpp", "go", "rust"],
        env="SUPPORTED_LANGUAGES"
    )
    max_file_size: int = Field(default=1024*1024, env="MAX_FILE_SIZE")  # 1MB
    
    # 索引配置
    chunk_size: int = Field(default=1000, env="CHUNK_SIZE")
    chunk_overlap: int = Field(default=200, env="CHUNK_OVERLAP")
    
    # 搜索配置
    search_k: int = Field(default=5, env="SEARCH_K")
    similarity_threshold: float = Field(default=0.7, env="SIMILARITY_THRESHOLD")
    
    # 缓存配置
    cache_enabled: bool = Field(default=True, env="CACHE_ENABLED")
    cache_ttl: int = Field(default=3600, env="CACHE_TTL")  # 1小时
    
    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: Optional[str] = Field(default=None, env="LOG_FILE")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()


# 支持的文件扩展名映射
LANGUAGE_EXTENSIONS = {
    "python": [".py", ".pyx", ".pyi"],
    "javascript": [".js", ".jsx", ".ts", ".tsx"],
    "java": [".java"],
    "cpp": [".cpp", ".cc", ".cxx", ".c", ".h", ".hpp"],
    "go": [".go"],
    "rust": [".rs"],
    "php": [".php"],
    "ruby": [".rb"],
    "swift": [".swift"],
    "kotlin": [".kt", ".kts"],
    "scala": [".scala"],
    "r": [".r", ".R"],
    "sql": [".sql"],
    "shell": [".sh", ".bash", ".zsh"],
    "yaml": [".yml", ".yaml"],
    "json": [".json"],
    "xml": [".xml"],
    "html": [".html", ".htm"],
    "css": [".css", ".scss", ".sass"],
    "markdown": [".md", ".markdown"],
}

# 忽略的文件和目录
IGNORE_PATTERNS = [
    "__pycache__",
    ".git",
    ".svn",
    ".hg",
    "node_modules",
    ".venv",
    "venv",
    ".env",
    "build",
    "dist",
    ".pytest_cache",
    ".mypy_cache",
    ".coverage",
    "*.pyc",
    "*.pyo",
    "*.pyd",
    ".DS_Store",
    "Thumbs.db",
    "*.log",
    "*.tmp",
    "*.temp",
]

# 代码质量检查工具配置
QUALITY_TOOLS = {
    "python": {
        "linter": "pylint",
        "formatter": "black",
        "type_checker": "mypy",
        "import_sorter": "isort",
    },
    "javascript": {
        "linter": "eslint",
        "formatter": "prettier",
        "type_checker": "typescript",
    },
    "java": {
        "linter": "checkstyle",
        "formatter": "google-java-format",
    },
}
