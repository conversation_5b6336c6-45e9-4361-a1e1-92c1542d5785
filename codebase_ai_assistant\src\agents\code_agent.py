"""
代码智能代理模块
基于LangChain实现的智能代码分析和问答代理
"""
from typing import List, Dict, Any, Optional
import logging

from langchain.agents import AgentExecutor, create_openai_functions_agent
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.schema import BaseMessage, HumanMessage, AIMessage
from langchain.tools import BaseTool, tool
from langchain.callbacks.base import BaseCallbackHandler

from ..core.config import settings
from ..core.model_factory import ModelFactory
from ..storage.vector_store import VectorStore, CodeDocument
from ..tools.code_tools import CodeAnalyzer, TestGenerator, DocumentGenerator

logger = logging.getLogger(__name__)


class CodeAgentCallbackHandler(BaseCallbackHandler):
    """代码代理回调处理器"""
    
    def __init__(self):
        self.logs = []
    
    def on_agent_action(self, action, **kwargs):
        self.logs.append(f"执行工具: {action.tool}")
        logger.info(f"代理执行工具: {action.tool}")
    
    def on_agent_finish(self, finish, **kwargs):
        self.logs.append("代理完成任务")
        logger.info("代理完成任务")


class CodeSearchTool(BaseTool):
    """代码搜索工具"""
    
    name = "code_search"
    description = "搜索代码库中的相关代码片段。输入搜索查询，返回相关的代码片段。"
    
    def __init__(self, vector_store: VectorStore):
        super().__init__()
        self.vector_store = vector_store
    
    def _run(self, query: str, k: int = 5) -> str:
        """执行代码搜索"""
        try:
            results = self.vector_store.search_similar_code(query, k=k)
            
            if not results:
                return "未找到相关代码片段。"
            
            output = []
            for i, doc in enumerate(results, 1):
                output.append(f"结果 {i}:")
                output.append(f"文件: {doc.file_path}")
                output.append(f"类型: {doc.element_type}")
                output.append(f"名称: {doc.element_name}")
                output.append(f"代码:\n{doc.page_content}")
                output.append("-" * 50)
            
            return "\n".join(output)
            
        except Exception as e:
            logger.error(f"代码搜索失败: {e}")
            return f"搜索失败: {str(e)}"


class FileAnalysisTool(BaseTool):
    """文件分析工具"""
    
    name = "file_analysis"
    description = "分析指定文件的结构和内容。输入文件路径，返回文件的详细分析。"
    
    def __init__(self, vector_store: VectorStore):
        super().__init__()
        self.vector_store = vector_store
        self.analyzer = CodeAnalyzer()
    
    def _run(self, file_path: str) -> str:
        """执行文件分析"""
        try:
            # 从向量存储中获取文件相关文档
            docs = self.vector_store.search_by_file(file_path)
            
            if not docs:
                return f"未找到文件: {file_path}"
            
            # 分析文件结构
            analysis = self.analyzer.analyze_file_structure(docs)
            
            output = []
            output.append(f"文件分析: {file_path}")
            output.append(f"语言: {analysis.get('language', 'unknown')}")
            output.append(f"总行数: {analysis.get('total_lines', 0)}")
            output.append(f"函数数量: {analysis.get('functions', 0)}")
            output.append(f"类数量: {analysis.get('classes', 0)}")
            output.append(f"复杂度: {analysis.get('complexity', 0)}")
            
            if analysis.get('functions_list'):
                output.append("\n函数列表:")
                for func in analysis['functions_list']:
                    output.append(f"  - {func}")
            
            if analysis.get('classes_list'):
                output.append("\n类列表:")
                for cls in analysis['classes_list']:
                    output.append(f"  - {cls}")
            
            return "\n".join(output)
            
        except Exception as e:
            logger.error(f"文件分析失败: {e}")
            return f"分析失败: {str(e)}"


class TestGenerationTool(BaseTool):
    """测试生成工具"""
    
    name = "generate_tests"
    description = "为指定的函数或类生成单元测试。输入函数/类名称，返回生成的测试代码。"
    
    def __init__(self, vector_store: VectorStore):
        super().__init__()
        self.vector_store = vector_store
        self.test_generator = TestGenerator()
    
    def _run(self, element_name: str, element_type: str = "function") -> str:
        """生成测试代码"""
        try:
            # 搜索目标元素
            query = f"{element_type} {element_name}"
            results = self.vector_store.search_similar_code(query, k=1)
            
            if not results:
                return f"未找到 {element_type}: {element_name}"
            
            doc = results[0]
            
            # 生成测试
            test_code = self.test_generator.generate_test(
                element_name=element_name,
                element_type=element_type,
                code_content=doc.page_content,
                language=doc.language
            )
            
            return f"为 {element_name} 生成的测试代码:\n\n{test_code}"
            
        except Exception as e:
            logger.error(f"测试生成失败: {e}")
            return f"生成失败: {str(e)}"


class DocumentationTool(BaseTool):
    """文档生成工具"""
    
    name = "generate_docs"
    description = "为代码生成文档。输入代码元素名称，返回生成的文档。"
    
    def __init__(self, vector_store: VectorStore):
        super().__init__()
        self.vector_store = vector_store
        self.doc_generator = DocumentGenerator()
    
    def _run(self, element_name: str, doc_type: str = "api") -> str:
        """生成文档"""
        try:
            # 搜索目标元素
            results = self.vector_store.search_similar_code(element_name, k=3)
            
            if not results:
                return f"未找到相关代码: {element_name}"
            
            # 生成文档
            documentation = self.doc_generator.generate_documentation(
                results=results,
                doc_type=doc_type
            )
            
            return f"生成的文档:\n\n{documentation}"
            
        except Exception as e:
            logger.error(f"文档生成失败: {e}")
            return f"生成失败: {str(e)}"


class CodeAgent:
    """智能代码代理"""
    
    def __init__(self, vector_store: VectorStore):
        self.vector_store = vector_store
        self.llm = ModelFactory.create_chat_model(temperature=0.1)
        
        # 初始化工具
        self.tools = [
            CodeSearchTool(vector_store),
            FileAnalysisTool(vector_store),
            TestGenerationTool(vector_store),
            DocumentationTool(vector_store),
        ]
        
        # 创建提示模板
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", self._get_system_prompt()),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad"),
        ])
        
        # 创建代理
        self.agent = create_openai_functions_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=self.prompt
        )
        
        # 创建代理执行器
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            verbose=True,
            max_iterations=5,
            callbacks=[CodeAgentCallbackHandler()]
        )
        
        # 聊天历史
        self.chat_history: List[BaseMessage] = []
    
    def _get_system_prompt(self) -> str:
        """获取系统提示"""
        return """你是一个智能代码助手，专门帮助开发者理解、分析和改进代码。

你的能力包括：
1. 搜索和分析代码库中的代码片段
2. 解释代码的功能和实现原理
3. 生成单元测试
4. 生成API文档
5. 提供代码优化建议
6. 回答关于代码的任何问题

使用工具时请注意：
- 使用code_search搜索相关代码
- 使用file_analysis分析文件结构
- 使用generate_tests生成测试代码
- 使用generate_docs生成文档

请用中文回答，并提供详细、准确的信息。"""
    
    def chat(self, message: str) -> str:
        """与代理对话"""
        try:
            # 执行代理
            response = self.agent_executor.invoke({
                "input": message,
                "chat_history": self.chat_history
            })
            
            # 更新聊天历史
            self.chat_history.append(HumanMessage(content=message))
            self.chat_history.append(AIMessage(content=response["output"]))
            
            # 限制历史长度
            if len(self.chat_history) > 20:
                self.chat_history = self.chat_history[-20:]
            
            return response["output"]
            
        except Exception as e:
            logger.error(f"代理对话失败: {e}")
            return f"抱歉，处理您的请求时出现错误: {str(e)}"
    
    def clear_history(self):
        """清空聊天历史"""
        self.chat_history = []
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return [tool.name for tool in self.tools]
